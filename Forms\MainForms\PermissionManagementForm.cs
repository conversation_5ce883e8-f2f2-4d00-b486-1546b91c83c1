using System;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;
using System.Linq;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using DevExpress.XtraGrid.Views.Grid;
using ProManage.Modules.Helpers.PermissionManagementForm;
using ProManage.Modules.Models;
using ProManage.Modules.Services;

namespace ProManage.Forms
{
    /// <summary>
    /// Comprehensive permission management form with 3 tabs for role permissions,
    /// user permissions, and global user management permissions.
    /// </summary>
    public partial class PermissionManagementForm : XtraForm
    {
        #region Private Fields

        private readonly PermissionGridHelper _gridHelper;
        private bool _isLoading = false;
        private int _currentRoleId = 0;
        private int _currentUserId = 0;
        private bool _hasUnsavedChanges = false;

        #endregion

        #region Constructor

        public PermissionManagementForm()
        {
            InitializeComponent();
            _gridHelper = new PermissionGridHelper();
            InitializeForm();
        }

        #endregion

        #region Form Initialization

        /// <summary>
        /// Initialize form components and setup
        /// </summary>
        private void InitializeForm()
        {
            try
            {
                // Set form properties for MDI
                this.MdiParent = Application.OpenForms.OfType<Form>().FirstOrDefault(f => f.IsMdiContainer);
                this.WindowState = FormWindowState.Maximized;

                // Initialize grids
                InitializeGrids();

                // Load initial data
                LoadRoles();
                LoadUsers();

                // Setup event handlers
                SetupEventHandlers();

                Debug.WriteLine("PermissionManagementForm initialized successfully");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error initializing Permission Management Form: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Initialize all grids with proper configuration
        /// </summary>
        private void InitializeGrids()
        {
            try
            {
                // Configure role permissions grid
                _gridHelper.ConfigureRolePermissionsGrid(gridControlRolePermissions, gridViewRolePermissions);

                // Configure user permissions grid
                _gridHelper.ConfigureUserPermissionsGrid(gridControlUserPermissions, gridViewUserPermissions);

                Debug.WriteLine("Grids initialized successfully");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error initializing grids: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Setup event handlers for form controls
        /// </summary>
        private void SetupEventHandlers()
        {
            try
            {
                // Role selection events
                cmbRoles.SelectedIndexChanged += CmbRoles_SelectedIndexChanged;

                // User selection events
                cmbUsers.SelectedIndexChanged += CmbUsers_SelectedIndexChanged;
                cmbUsersGlobal.SelectedIndexChanged += CmbUsersGlobal_SelectedIndexChanged;

                // Tab change events
                tabControlMain.SelectedPageChanged += TabControlMain_SelectedPageChanged;

                // Grid events for tracking changes
                gridViewRolePermissions.CellValueChanged += GridViewRolePermissions_CellValueChanged;
                gridViewUserPermissions.CellValueChanged += GridViewUserPermissions_CellValueChanged;

                // Button events
                btnSave.Click += BtnSave_Click;
                btnCancel.Click += BtnCancel_Click;
                btnRefresh.Click += BtnRefresh_Click;
                btnResetUserPermissions.Click += BtnResetUserPermissions_Click;
                btnCopyFromRole.Click += BtnCopyFromRole_Click;

                // Global permission checkboxes
                chkCanCreateUsers.CheckedChanged += GlobalPermission_CheckedChanged;
                chkCanEditUsers.CheckedChanged += GlobalPermission_CheckedChanged;
                chkCanDeleteUsers.CheckedChanged += GlobalPermission_CheckedChanged;
                chkCanPrintUsers.CheckedChanged += GlobalPermission_CheckedChanged;

                Debug.WriteLine("Event handlers setup successfully");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error setting up event handlers: {ex.Message}");
                throw;
            }
        }

        #endregion

        #region Data Loading

        /// <summary>
        /// Load roles into dropdown
        /// </summary>
        private void LoadRoles()
        {
            try
            {
                _isLoading = true;

                var roles = PermissionService.GetAllRoles();
                cmbRoles.Properties.Items.Clear();
                cmbRoles.Properties.Items.AddRange(roles.Select(r => $"{r.RoleId}|{r.RoleName}").ToArray());

                if (roles.Any())
                {
                    cmbRoles.SelectedIndex = 0;
                }

                Debug.WriteLine($"Loaded {roles.Count} roles");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading roles: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                _isLoading = false;
            }
        }

        /// <summary>
        /// Load users into dropdown
        /// </summary>
        private void LoadUsers()
        {
            try
            {
                _isLoading = true;

                var users = PermissionService.GetAllUsers();
                cmbUsers.Properties.Items.Clear();
                cmbUsers.Properties.Items.AddRange(users.Select(u => $"{u.UserId}|{u.Username} - {u.FullName}").ToArray());

                // Also populate the global permissions user combo
                cmbUsersGlobal.Properties.Items.Clear();
                cmbUsersGlobal.Properties.Items.AddRange(users.Select(u => $"{u.UserId}|{u.Username} - {u.FullName}").ToArray());

                if (users.Any())
                {
                    cmbUsers.SelectedIndex = 0;
                    cmbUsersGlobal.SelectedIndex = 0;
                }

                Debug.WriteLine($"Loaded {users.Count} users");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading users: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                _isLoading = false;
            }
        }

        /// <summary>
        /// Load role permissions for selected role
        /// </summary>
        private void LoadRolePermissions()
        {
            if (_currentRoleId <= 0) return;

            try
            {
                _isLoading = true;
                _gridHelper.LoadRolePermissions(gridControlRolePermissions, _currentRoleId);
                Debug.WriteLine($"Loaded permissions for role ID: {_currentRoleId}");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading role permissions: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                _isLoading = false;
            }
        }

        /// <summary>
        /// Load user permissions for selected user
        /// </summary>
        private void LoadUserPermissions()
        {
            if (_currentUserId <= 0) return;

            try
            {
                _isLoading = true;
                _gridHelper.LoadUserPermissions(gridControlUserPermissions, _currentUserId);
                LoadGlobalPermissions();
                Debug.WriteLine($"Loaded permissions for user ID: {_currentUserId}");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading user permissions: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                _isLoading = false;
            }
        }

        /// <summary>
        /// Load global permissions for selected user
        /// </summary>
        private void LoadGlobalPermissions()
        {
            if (_currentUserId <= 0) return;

            try
            {
                _isLoading = true;

                var globalPermissions = PermissionService.GetGlobalPermissions(_currentUserId);
                
                chkCanCreateUsers.Checked = globalPermissions?.CanCreateUsers ?? false;
                chkCanEditUsers.Checked = globalPermissions?.CanEditUsers ?? false;
                chkCanDeleteUsers.Checked = globalPermissions?.CanDeleteUsers ?? false;
                chkCanPrintUsers.Checked = globalPermissions?.CanPrintUsers ?? false;

                Debug.WriteLine($"Loaded global permissions for user ID: {_currentUserId}");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading global permissions: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                _isLoading = false;
            }
        }

        #endregion

        #region Event Handlers

        /// <summary>
        /// Handle role selection change
        /// </summary>
        private void CmbRoles_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (_isLoading) return;

            try
            {
                if (cmbRoles.SelectedItem != null)
                {
                    var selectedValue = cmbRoles.SelectedItem.ToString();
                    var parts = selectedValue.Split('|');
                    if (parts.Length >= 2 && int.TryParse(parts[0], out int roleId))
                    {
                        _currentRoleId = roleId;
                        LoadRolePermissions();
                        _hasUnsavedChanges = false;
                        UpdateButtonStates();
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error handling role selection: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Handle user selection change
        /// </summary>
        private void CmbUsers_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (_isLoading) return;

            try
            {
                if (cmbUsers.SelectedItem != null)
                {
                    var selectedValue = cmbUsers.SelectedItem.ToString();
                    var parts = selectedValue.Split('|');
                    if (parts.Length >= 2 && int.TryParse(parts[0], out int userId))
                    {
                        _currentUserId = userId;
                        LoadUserPermissions();
                        _hasUnsavedChanges = false;
                        UpdateButtonStates();

                        // Synchronize with global permissions combo
                        SynchronizeUserSelection(userId);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error handling user selection: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Handle global permissions user selection change
        /// </summary>
        private void CmbUsersGlobal_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (_isLoading) return;

            try
            {
                if (cmbUsersGlobal.SelectedItem != null)
                {
                    var selectedValue = cmbUsersGlobal.SelectedItem.ToString();
                    var parts = selectedValue.Split('|');
                    if (parts.Length >= 2 && int.TryParse(parts[0], out int userId))
                    {
                        _currentUserId = userId;
                        LoadGlobalPermissions();
                        _hasUnsavedChanges = false;
                        UpdateButtonStates();

                        // Synchronize with user permissions combo
                        SynchronizeUserSelection(userId);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error handling global user selection: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Handle tab change to synchronize user selection
        /// </summary>
        private void TabControlMain_SelectedPageChanged(object sender, DevExpress.XtraTab.TabPageChangedEventArgs e)
        {
            try
            {
                // Synchronize user selection between tabs
                if (e.Page == tabPageUserPermissions && _currentUserId > 0)
                {
                    LoadUserPermissions();
                }
                else if (e.Page == tabPageGlobalPermissions && _currentUserId > 0)
                {
                    LoadGlobalPermissions();
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error handling tab change: {ex.Message}");
            }
        }

        /// <summary>
        /// Handle role permissions grid cell value changes
        /// </summary>
        private void GridViewRolePermissions_CellValueChanged(object sender, DevExpress.XtraGrid.Views.Base.CellValueChangedEventArgs e)
        {
            if (!_isLoading)
            {
                _hasUnsavedChanges = true;
                UpdateButtonStates();
            }
        }

        /// <summary>
        /// Handle user permissions grid cell value changes
        /// </summary>
        private void GridViewUserPermissions_CellValueChanged(object sender, DevExpress.XtraGrid.Views.Base.CellValueChangedEventArgs e)
        {
            if (!_isLoading)
            {
                _hasUnsavedChanges = true;
                UpdateButtonStates();
                
                // Recalculate effective permissions if user override changed
                if (e.Column.FieldName.StartsWith("User"))
                {
                    RecalculateEffectivePermissions(e.RowHandle);
                }
            }
        }

        /// <summary>
        /// Handle global permission checkbox changes
        /// </summary>
        private void GlobalPermission_CheckedChanged(object sender, EventArgs e)
        {
            if (!_isLoading)
            {
                _hasUnsavedChanges = true;
                UpdateButtonStates();
            }
        }

        #endregion

        #region Button Event Handlers

        /// <summary>
        /// Handle Save button click
        /// </summary>
        private void BtnSave_Click(object sender, EventArgs e)
        {
            try
            {
                if (SaveChanges())
                {
                    _hasUnsavedChanges = false;
                    UpdateButtonStates();
                    MessageBox.Show("Changes saved successfully.", "Success",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error saving changes: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Handle Cancel button click
        /// </summary>
        private void BtnCancel_Click(object sender, EventArgs e)
        {
            try
            {
                if (_hasUnsavedChanges)
                {
                    var result = MessageBox.Show("You have unsaved changes. Are you sure you want to cancel?",
                        "Confirm Cancel", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                    
                    if (result == DialogResult.No)
                        return;
                }

                // Reload current data
                if (tabControlMain.SelectedTabPage == tabPageRolePermissions)
                {
                    LoadRolePermissions();
                }
                else if (tabControlMain.SelectedTabPage == tabPageUserPermissions)
                {
                    LoadUserPermissions();
                }
                else if (tabControlMain.SelectedTabPage == tabPageGlobalPermissions)
                {
                    LoadGlobalPermissions();
                }

                _hasUnsavedChanges = false;
                UpdateButtonStates();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error canceling changes: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Handle Refresh button click
        /// </summary>
        private void BtnRefresh_Click(object sender, EventArgs e)
        {
            try
            {
                LoadRoles();
                LoadUsers();
                
                if (tabControlMain.SelectedTabPage == tabPageRolePermissions)
                {
                    LoadRolePermissions();
                }
                else if (tabControlMain.SelectedTabPage == tabPageUserPermissions)
                {
                    LoadUserPermissions();
                }
                else if (tabControlMain.SelectedTabPage == tabPageGlobalPermissions)
                {
                    LoadGlobalPermissions();
                }

                _hasUnsavedChanges = false;
                UpdateButtonStates();
                
                MessageBox.Show("Data refreshed successfully.", "Success",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error refreshing data: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Handle Reset User Permissions button click
        /// </summary>
        private void BtnResetUserPermissions_Click(object sender, EventArgs e)
        {
            try
            {
                if (_currentUserId <= 0)
                {
                    MessageBox.Show("Please select a user first.", "Information",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                var result = MessageBox.Show(
                    "This will remove all user permission overrides and revert to role permissions. Are you sure?",
                    "Confirm Reset", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    PermissionService.ResetUserPermissions(_currentUserId);
                    PermissionService.ClearCache();
                    LoadUserPermissions();
                    
                    _hasUnsavedChanges = false;
                    UpdateButtonStates();
                    
                    MessageBox.Show("User permissions reset successfully.", "Success",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error resetting user permissions: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Handle Copy From Role button click
        /// </summary>
        private void BtnCopyFromRole_Click(object sender, EventArgs e)
        {
            try
            {
                // This would open a dialog to select a role to copy from
                // For now, show a placeholder message
                MessageBox.Show("Copy from role functionality will be implemented in the next iteration.",
                    "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error copying from role: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region Helper Methods

        /// <summary>
        /// Update button states based on current form state
        /// </summary>
        private void UpdateButtonStates()
        {
            btnSave.Enabled = _hasUnsavedChanges;
            btnCancel.Enabled = _hasUnsavedChanges;
        }

        /// <summary>
        /// Recalculate effective permissions for a specific row
        /// </summary>
        private void RecalculateEffectivePermissions(int rowHandle)
        {
            try
            {
                var gridView = gridViewUserPermissions;

                // Get role and user permissions for this row
                var roleRead = (bool)gridView.GetRowCellValue(rowHandle, "RoleRead");
                var roleNew = (bool)gridView.GetRowCellValue(rowHandle, "RoleNew");
                var roleEdit = (bool)gridView.GetRowCellValue(rowHandle, "RoleEdit");
                var roleDelete = (bool)gridView.GetRowCellValue(rowHandle, "RoleDelete");
                var rolePrint = (bool)gridView.GetRowCellValue(rowHandle, "RolePrint");

                var userRead = gridView.GetRowCellValue(rowHandle, "UserRead") as bool?;
                var userNew = gridView.GetRowCellValue(rowHandle, "UserNew") as bool?;
                var userEdit = gridView.GetRowCellValue(rowHandle, "UserEdit") as bool?;
                var userDelete = gridView.GetRowCellValue(rowHandle, "UserDelete") as bool?;
                var userPrint = gridView.GetRowCellValue(rowHandle, "UserPrint") as bool?;

                // Calculate effective permissions (user override takes precedence)
                gridView.SetRowCellValue(rowHandle, "EffectiveRead", userRead ?? roleRead);
                gridView.SetRowCellValue(rowHandle, "EffectiveNew", userNew ?? roleNew);
                gridView.SetRowCellValue(rowHandle, "EffectiveEdit", userEdit ?? roleEdit);
                gridView.SetRowCellValue(rowHandle, "EffectiveDelete", userDelete ?? roleDelete);
                gridView.SetRowCellValue(rowHandle, "EffectivePrint", userPrint ?? rolePrint);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error recalculating effective permissions: {ex.Message}");
            }
        }

        /// <summary>
        /// Save all changes based on current tab
        /// </summary>
        private bool SaveChanges()
        {
            try
            {
                if (tabControlMain.SelectedTabPage == tabPageRolePermissions)
                {
                    return SaveRolePermissions();
                }
                else if (tabControlMain.SelectedTabPage == tabPageUserPermissions)
                {
                    return SaveUserPermissions();
                }
                else if (tabControlMain.SelectedTabPage == tabPageGlobalPermissions)
                {
                    return SaveGlobalPermissions();
                }

                return false;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error saving changes: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }
        }

        /// <summary>
        /// Save role permissions from grid
        /// </summary>
        private bool SaveRolePermissions()
        {
            if (_currentRoleId <= 0) return false;

            try
            {
                var permissions = new List<RolePermissionUpdate>();
                var gridView = gridViewRolePermissions;

                for (int i = 0; i < gridView.RowCount; i++)
                {
                    var formName = gridView.GetRowCellValue(i, "FormName")?.ToString();
                    if (string.IsNullOrEmpty(formName)) continue;

                    var permission = new RolePermissionUpdate
                    {
                        RoleId = _currentRoleId,
                        FormName = formName,
                        ReadPermission = (bool)gridView.GetRowCellValue(i, "ReadPermission"),
                        NewPermission = (bool)gridView.GetRowCellValue(i, "NewPermission"),
                        EditPermission = (bool)gridView.GetRowCellValue(i, "EditPermission"),
                        DeletePermission = (bool)gridView.GetRowCellValue(i, "DeletePermission"),
                        PrintPermission = (bool)gridView.GetRowCellValue(i, "PrintPermission")
                    };

                    permissions.Add(permission);
                }

                PermissionService.UpdateRolePermissions(_currentRoleId, permissions);
                PermissionService.ClearCache();

                return true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error saving role permissions: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }
        }

        /// <summary>
        /// Save user permissions from grid
        /// </summary>
        private bool SaveUserPermissions()
        {
            if (_currentUserId <= 0) return false;

            try
            {
                var permissions = new List<UserPermissionUpdate>();
                var gridView = gridViewUserPermissions;

                for (int i = 0; i < gridView.RowCount; i++)
                {
                    var formName = gridView.GetRowCellValue(i, "FormName")?.ToString();
                    if (string.IsNullOrEmpty(formName)) continue;

                    var permission = new UserPermissionUpdate
                    {
                        UserId = _currentUserId,
                        FormName = formName,
                        ReadPermission = gridView.GetRowCellValue(i, "UserRead") as bool?,
                        NewPermission = gridView.GetRowCellValue(i, "UserNew") as bool?,
                        EditPermission = gridView.GetRowCellValue(i, "UserEdit") as bool?,
                        DeletePermission = gridView.GetRowCellValue(i, "UserDelete") as bool?,
                        PrintPermission = gridView.GetRowCellValue(i, "UserPrint") as bool?
                    };

                    permissions.Add(permission);
                }

                PermissionService.UpdateUserPermissions(_currentUserId, permissions);
                PermissionService.ClearCache();

                return true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error saving user permissions: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }
        }

        /// <summary>
        /// Save global permissions
        /// </summary>
        private bool SaveGlobalPermissions()
        {
            if (_currentUserId <= 0) return false;

            try
            {
                var globalPermissions = new GlobalPermissionUpdate
                {
                    UserId = _currentUserId,
                    CanCreateUsers = chkCanCreateUsers.Checked,
                    CanEditUsers = chkCanEditUsers.Checked,
                    CanDeleteUsers = chkCanDeleteUsers.Checked,
                    CanPrintUsers = chkCanPrintUsers.Checked
                };

                PermissionService.UpdateGlobalPermissions(globalPermissions);
                PermissionService.ClearCache();

                return true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error saving global permissions: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }
        }

        /// <summary>
        /// Handle form closing to check for unsaved changes
        /// </summary>
        protected override void OnFormClosing(FormClosingEventArgs e)
        {
            if (_hasUnsavedChanges)
            {
                var result = MessageBox.Show("You have unsaved changes. Do you want to save before closing?",
                    "Unsaved Changes", MessageBoxButtons.YesNoCancel, MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    if (!SaveChanges())
                    {
                        e.Cancel = true;
                        return;
                    }
                }
                else if (result == DialogResult.Cancel)
                {
                    e.Cancel = true;
                    return;
                }
            }

            base.OnFormClosing(e);
        }

        #endregion

        #region Helper Methods

        /// <summary>
        /// Recalculate effective permissions for a specific row
        /// </summary>
        private void RecalculateEffectivePermissions(int rowHandle)
        {
            try
            {
                var gridView = gridViewUserPermissions;

                // Get role and user permissions for this row
                var roleRead = (bool)gridView.GetRowCellValue(rowHandle, "RoleRead");
                var roleNew = (bool)gridView.GetRowCellValue(rowHandle, "RoleNew");
                var roleEdit = (bool)gridView.GetRowCellValue(rowHandle, "RoleEdit");
                var roleDelete = (bool)gridView.GetRowCellValue(rowHandle, "RoleDelete");
                var rolePrint = (bool)gridView.GetRowCellValue(rowHandle, "RolePrint");

                var userRead = gridView.GetRowCellValue(rowHandle, "UserRead") as bool?;
                var userNew = gridView.GetRowCellValue(rowHandle, "UserNew") as bool?;
                var userEdit = gridView.GetRowCellValue(rowHandle, "UserEdit") as bool?;
                var userDelete = gridView.GetRowCellValue(rowHandle, "UserDelete") as bool?;
                var userPrint = gridView.GetRowCellValue(rowHandle, "UserPrint") as bool?;

                // Calculate effective permissions (user override takes precedence)
                gridView.SetRowCellValue(rowHandle, "EffectiveRead", userRead ?? roleRead);
                gridView.SetRowCellValue(rowHandle, "EffectiveNew", userNew ?? roleNew);
                gridView.SetRowCellValue(rowHandle, "EffectiveEdit", userEdit ?? roleEdit);
                gridView.SetRowCellValue(rowHandle, "EffectiveDelete", userDelete ?? roleDelete);
                gridView.SetRowCellValue(rowHandle, "EffectivePrint", userPrint ?? rolePrint);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error recalculating effective permissions: {ex.Message}");
            }
        }

        /// <summary>
        /// Save all changes based on current tab
        /// </summary>
        public bool SaveChanges()
        {
            try
            {
                if (tabControlMain?.SelectedTabPage == tabPageRolePermissions)
                {
                    return SaveRolePermissions();
                }
                else if (tabControlMain?.SelectedTabPage == tabPageUserPermissions)
                {
                    return SaveUserPermissions();
                }
                else if (tabControlMain?.SelectedTabPage == tabPageGlobalPermissions)
                {
                    return SaveGlobalPermissions();
                }

                return false;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error saving changes: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }
        }

        /// <summary>
        /// Save role permissions from grid
        /// </summary>
        private bool SaveRolePermissions()
        {
            if (_currentRoleId <= 0) return false;

            try
            {
                var permissions = new List<RolePermissionUpdate>();
                var gridView = gridViewRolePermissions;

                for (int i = 0; i < gridView.RowCount; i++)
                {
                    var formName = gridView.GetRowCellValue(i, "FormName")?.ToString();
                    if (string.IsNullOrEmpty(formName)) continue;

                    var permission = new RolePermissionUpdate
                    {
                        RoleId = _currentRoleId,
                        FormName = formName,
                        ReadPermission = (bool)gridView.GetRowCellValue(i, "ReadPermission"),
                        NewPermission = (bool)gridView.GetRowCellValue(i, "NewPermission"),
                        EditPermission = (bool)gridView.GetRowCellValue(i, "EditPermission"),
                        DeletePermission = (bool)gridView.GetRowCellValue(i, "DeletePermission"),
                        PrintPermission = (bool)gridView.GetRowCellValue(i, "PrintPermission")
                    };

                    permissions.Add(permission);
                }

                PermissionService.UpdateRolePermissions(_currentRoleId, permissions);
                PermissionService.ClearCache();

                _hasUnsavedChanges = false;
                return true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error saving role permissions: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }
        }

        /// <summary>
        /// Save user permissions from grid
        /// </summary>
        private bool SaveUserPermissions()
        {
            if (_currentUserId <= 0) return false;

            try
            {
                var permissions = new List<UserPermissionUpdate>();
                var gridView = gridViewUserPermissions;

                for (int i = 0; i < gridView.RowCount; i++)
                {
                    var formName = gridView.GetRowCellValue(i, "FormName")?.ToString();
                    if (string.IsNullOrEmpty(formName)) continue;

                    var permission = new UserPermissionUpdate
                    {
                        UserId = _currentUserId,
                        FormName = formName,
                        ReadPermission = gridView.GetRowCellValue(i, "UserRead") as bool?,
                        NewPermission = gridView.GetRowCellValue(i, "UserNew") as bool?,
                        EditPermission = gridView.GetRowCellValue(i, "UserEdit") as bool?,
                        DeletePermission = gridView.GetRowCellValue(i, "UserDelete") as bool?,
                        PrintPermission = gridView.GetRowCellValue(i, "UserPrint") as bool?
                    };

                    permissions.Add(permission);
                }

                PermissionService.UpdateUserPermissions(_currentUserId, permissions);
                PermissionService.ClearCache();

                _hasUnsavedChanges = false;
                return true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error saving user permissions: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }
        }

        /// <summary>
        /// Save global permissions
        /// </summary>
        private bool SaveGlobalPermissions()
        {
            if (_currentUserId <= 0) return false;

            try
            {
                var globalPermissions = new GlobalPermissionUpdate
                {
                    UserId = _currentUserId,
                    CanCreateUsers = chkCanCreateUsers?.Checked ?? false,
                    CanEditUsers = chkCanEditUsers?.Checked ?? false,
                    CanDeleteUsers = chkCanDeleteUsers?.Checked ?? false,
                    CanPrintUsers = chkCanPrintUsers?.Checked ?? false
                };

                PermissionService.UpdateGlobalPermissions(globalPermissions);
                PermissionService.ClearCache();

                _hasUnsavedChanges = false;
                return true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error saving global permissions: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }
        }

        /// <summary>
        /// Check if there are unsaved changes
        /// </summary>
        public bool HasUnsavedChanges()
        {
            return _hasUnsavedChanges;
        }

        /// <summary>
        /// Synchronize user selection between tabs
        /// </summary>
        private void SynchronizeUserSelection(int userId)
        {
            try
            {
                _isLoading = true;

                // Find the matching item in user combo box
                var userItem = $"{userId}|";

                // Update cmbUsers if not already selected
                for (int i = 0; i < cmbUsers.Properties.Items.Count; i++)
                {
                    if (cmbUsers.Properties.Items[i].ToString().StartsWith(userItem))
                    {
                        if (cmbUsers.SelectedIndex != i)
                            cmbUsers.SelectedIndex = i;
                        break;
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error synchronizing user selection: {ex.Message}");
            }
            finally
            {
                _isLoading = false;
            }
        }

        #endregion
    }
}
