# Task 12: Role Creation and Management System

## Objective
**CORRECTED IMPLEMENTATION:** Integrate role creation and management functionality into existing PermissionManagementForm as "Add Role" tab with MenuRibbon UC, following user's specific requirements.

## Priority
**UI MANAGEMENT** - Depends on Tasks 01-11

## Estimated Time
3 hours

## Dependencies
- Task 02: Permission Data Models Creation
- Task 04: Database Connection Service for Permissions
- Task 06: Core Permission Service Logic
- Task 09: Permission Management Form (2-Tab UI)

## Current Problem
- No comprehensive role creation functionality in current implementation
- User specifically requested integration into PermissionManagementForm as a tab, NOT as separate forms or dialogs
- Missing role management UI and validation within existing permission management interface

## Files Removed (Incorrect Implementation)
- ❌ `Forms/Dialogs/RoleCreateEditDialog.cs` (REMOVED - user requested tab integration, not dialog)
- ❌ `Forms/Dialogs/RoleCreateEditDialog.Designer.cs` (REMOVED - user requested tab integration, not dialog)
- ❌ `Forms/Dialogs/RoleCreateEditDialog.resx` (REMOVED - user requested tab integration, not dialog)

## Files to Modify (Corrected Approach)
- 🔄 `Forms/MainForms/PermissionManagementForm.cs` (add "Add Role" tab functionality)
- ✅ `Forms/MainForms/PermissionManagementForm.Designer.cs` (add "Add Role" tab with MenuRibbon UC)
- ✅ `Modules/Connections/PermissionDatabaseService.cs` (add role CRUD methods)
- 🔄 `ProManage.csproj` (remove dialog references, ensure proper compilation)

## Implementation Plan

### Phase 1: Database Service Enhancement
Add role CRUD methods to `PermissionDatabaseService.cs`:

```csharp
/// <summary>
/// Create a new role
/// </summary>
public static int CreateRole(RoleCreateRequest request)
{
    const string query = @"
        INSERT INTO roles (role_name, description, is_active)
        VALUES (@roleName, @description, @isActive)
        RETURNING role_id";
    
    try
    {
        using (var connection = DatabaseConnectionManager.Instance.CreateNewConnection())
        {
            connection.Open();
            using (var transaction = connection.BeginTransaction())
            {
                try
                {
                    using (var command = new NpgsqlCommand(query, connection, transaction))
                    {
                        command.Parameters.AddWithValue("@roleName", request.RoleName);
                        command.Parameters.AddWithValue("@description", request.Description ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@isActive", request.IsActive);
                        
                        var roleId = (int)command.ExecuteScalar();
                        
                        // Create default permissions for all forms
                        CreateDefaultPermissionsForRole(roleId, connection, transaction);
                        
                        // Copy permissions from source role if specified
                        if (request.CopyFromRoleId.HasValue)
                        {
                            CopyRolePermissions(request.CopyFromRoleId.Value, roleId, connection, transaction);
                        }
                        
                        transaction.Commit();
                        return roleId;
                    }
                }
                catch
                {
                    transaction.Rollback();
                    throw;
                }
            }
        }
    }
    catch (Exception ex)
    {
        Debug.WriteLine($"Error creating role: {ex.Message}");
        throw;
    }
}

/// <summary>
/// Update an existing role
/// </summary>
public static bool UpdateRole(RoleUpdateRequest request)

/// <summary>
/// Delete a role (only if not in use)
/// </summary>
public static bool DeleteRole(int roleId)

/// <summary>
/// Check if role is currently assigned to users
/// </summary>
public static bool IsRoleInUse(int roleId)

/// <summary>
/// Get all roles with user count
/// </summary>
public static List<RoleWithUserCount> GetAllRolesWithUserCount()

/// <summary>
/// Copy permissions from one role to another
/// </summary>
public static bool CopyRolePermissions(int sourceRoleId, int targetRoleId)
```

### Phase 2: PermissionManagementForm "Add Role" Tab Enhancement
**CORRECTED APPROACH:** Enhance existing PermissionManagementForm with "Add Role" tab functionality:

#### Tab Features:
- "Add Role" tab with MenuRibbon UC for role operations
- Role listing grid with columns:
  - Role Name
  - Description
  - Active Status
  - Created Date
  - User Count
- MenuRibbon UC configured for role management:
  - New Role
  - Edit Role
  - Delete Role
  - Copy Permissions
  - Refresh
- Double-click row to edit role
- Integration with existing form's save/cancel/refresh buttons

#### Grid Setup in "Add Role" Tab:
```csharp
private void SetupRoleGrid()
{
    gridViewRoles.OptionsView.ShowGroupPanel = false;
    gridViewRoles.OptionsSelection.EnableAppearanceFocusedCell = false;
    gridViewRoles.OptionsCustomization.AllowColumnMoving = false;

    // Setup columns
    var colRoleName = gridViewRoles.Columns.Add();
    colRoleName.FieldName = "RoleName";
    colRoleName.Caption = "Role Name";
    colRoleName.Width = 150;

    var colDescription = gridViewRoles.Columns.Add();
    colDescription.FieldName = "Description";
    colDescription.Caption = "Description";
    colDescription.Width = 250;

    var colIsActive = gridViewRoles.Columns.Add();
    colIsActive.FieldName = "IsActive";
    colIsActive.Caption = "Active";
    colIsActive.Width = 80;
    colIsActive.ColumnEdit = new CheckEdit();

    var colUserCount = gridViewRoles.Columns.Add();
    colUserCount.FieldName = "UserCount";
    colUserCount.Caption = "Users";
    colUserCount.Width = 80;
    colUserCount.AppearanceCell.TextOptions.HAlignment = HorzAlignment.Center;

    var colCreatedDate = gridViewRoles.Columns.Add();
    colCreatedDate.FieldName = "CreatedDate";
    colCreatedDate.Caption = "Created";
    colCreatedDate.Width = 120;
    colCreatedDate.DisplayFormat.FormatType = FormatType.DateTime;
    colCreatedDate.DisplayFormat.FormatString = "dd/MM/yyyy";
}
```

### Phase 3: Inline Role Creation/Editing
**CORRECTED APPROACH:** Implement role creation/editing within the "Add Role" tab using popup dialogs or inline editing:

#### Inline Editing Features:
- Simple popup dialogs for role creation/editing
- Fields:
  - Role Name (required, unique validation)
  - Description (optional, multi-line)
  - Active checkbox
- Initial permissions options:
  - No permissions (recommended)
  - Copy from existing role
- Integration with MenuRibbon UC events
- Real-time validation

#### Validation Rules:
- Role names must be unique (case-insensitive)
- Role names cannot be empty or whitespace
- Cannot modify system roles (Administrator, Manager, User, ReadOnly)
- Description limited to 500 characters

### Phase 4: Role Management Integration
**CORRECTED APPROACH:** Integrate role management functionality into existing PermissionManagementForm:

```csharp
// Add to PermissionManagementForm.cs
private void SetupCreateRoleTab()
{
    // Configure MenuRibbon UC for role management
    menuRibbonCreateRole.ConfigureForFormType("rolemanagement");
    menuRibbonCreateRole.FormName = "RoleManagement";
    menuRibbonCreateRole.CurrentUserId = _currentUserId;

    // Wire up events
    menuRibbonCreateRole.NewClicked += MenuRibbonCreateRole_NewClicked;
    menuRibbonCreateRole.EditClicked += MenuRibbonCreateRole_EditClicked;
    menuRibbonCreateRole.DeleteClicked += MenuRibbonCreateRole_DeleteClicked;
    menuRibbonCreateRole.SaveClicked += MenuRibbonCreateRole_SaveClicked;
    menuRibbonCreateRole.CancelClicked += MenuRibbonCreateRole_CancelClicked;

    // Setup role grid
    SetupRoleGrid();
    LoadRoles();
}
```

## Acceptance Criteria (Corrected Implementation)
- 🔄 "Add Role" tab integrated into PermissionManagementForm with role listing grid (Name, Description, Active, Created Date, User Count)
- 🔄 Role CRUD operations: Create, Edit, Delete, Copy Permissions via MenuRibbon UC
- 🔄 Inline role creation/editing functionality with validation (NO separate dialog forms)
- 🔄 Role name uniqueness validation
- 🔄 Prevent deletion of roles currently assigned to users
- 🔄 Automatic permission setup for new roles (default to no permissions)
- 🔄 Copy permissions from existing role functionality
- ✅ Integration with existing permission system
- 🔄 MenuRibbon UC integration for consistent interface
- 🔄 Proper error handling and user feedback
- 🔄 Cannot delete system roles (Administrator, Manager, User, ReadOnly)
- 🔄 Real-time validation in popup dialogs
- 🔄 Context menu support in grid
- 🔄 Double-click to edit functionality

## Integration Points (Corrected)
- ✅ "Add Role" tab already integrated into PermissionManagementForm
- 🔄 Integrate with existing permission cache clearing
- 🔄 Update role dropdowns in other forms when roles are added/modified
- 🔄 Ensure permission system recognizes new roles immediately
- 🔄 MenuRibbon UC event handling for role operations

## Error Handling
- Graceful handling of database connection issues
- User-friendly error messages for validation failures
- Prevention of system lockout scenarios
- Proper transaction rollback on failures
- Audit trail for role creation/modification/deletion

## Testing Scenarios
1. Create new role with no permissions
2. Create new role copying from existing role
3. Edit existing role details
4. Attempt to delete role in use (should fail)
5. Delete unused role (should succeed)
6. Validate role name uniqueness
7. Test system role protection
8. Verify permission copying works correctly
9. Test grid refresh after operations
10. Verify integration with permission system

This task fills the critical gap in role management functionality and provides a complete solution for role creation and management within the RBAC system.
